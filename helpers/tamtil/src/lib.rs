//! # TAMTIL - The Actor Model That Is Like
//!
//! A high-performance actor system with rkyv zero-copy serialization, designed like an opera
//! where components work independently but in harmony.
//!
//! ## Table of Contents
//!
//! 1. [Core Concepts](#core-concepts)
//! 2. [Architecture](#architecture)
//! 3. [Addressing System](#addressing-system)
//! 4. [Developer API](#developer-api)
//! 5. [Platform Module](#platform-module)
//! 6. [Context Module](#context-module)
//! 7. [Actor Module](#actor-module)
//! 8. [Infrastructure](#infrastructure)
//! 9. [Complete Example](#complete-example)
//! 10. [Tests](#tests)
//!
//! ## Core Concepts
//!
//! Everything is an actor following the universal pattern: `actor.act(action, actors)`
//!
//! ### Key Features
//! - **Alice <PERSON>'s actor pattern** with tokio hidden behind platform abstraction
//! - **rkyv zero-copy serialization** for maximum performance
//! - **Single-word naming** convention throughout (unless impossible)
//! - **Hierarchical actor spawning** via `actors.spawn(name)`
//! - **URL-based addressing** with full hierarchy support
//! - **Action→reaction pattern** for all interactions
//! - **No locking** - actors handle concurrency elegantly
//! - **Automatic child cleanup** when parent stops
//!
//! ## Architecture
//!
//! ```text
//! Platform (platform.com)
//! └── Context (platform.com/web)
//!     └── Context Instance (platform.com/web/main)
//!         └── Actor (platform.com/web/main/counter)
//!             └── Actor Instance (platform.com/web/main/counter/stats)
//!                 └── Child Actor (platform.com/web/main/counter/stats/session)
//!                     └── Child Instance (platform.com/web/main/counter/stats/session/user123)
//! ```
//!
//! ## Addressing System
//!
//! TAMTIL supports hierarchical URL-based addressing up to 6+ levels:
//! - `platform.com`
//! - `platform.com/context_name`
//! - `platform.com/context_name/context_id`
//! - `platform.com/context_name/context_id/actor_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id`
//! - And so on...
//!
//! ## Developer API
//!
//! Primary interactions:
//! - `actors.spawn(name)` - spawn child actor
//! - `actors.actor(id).act(action)` - send action to actor
//! - All actors receive `actors` parameter for spawning and communication

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;
use serde_json::Value;

// ============================================================================
// CHAPTER 1: CORE TYPES AND TRAITS
// ============================================================================

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
    #[error("Invalid address format: {address}")]
    InvalidAddress { address: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// MEMORY SYSTEM - Graph-based Actor State Management
// ============================================================================

/// Memory operations that reactions can perform
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryOperation {
    /// Create a new memory node
    Create { key: String, value: Value },
    /// Update an existing memory node
    Update { key: String, value: Value },
    /// Delete a memory node
    Delete { key: String },
    /// Create a relationship between two memory nodes
    Link { from: String, to: String, relation: String },
    /// Remove a relationship between two memory nodes
    Unlink { from: String, to: String, relation: String },
}

/// A memory node in the actor's memory graph
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct MemoryNode {
    pub key: String,
    pub value: Value,
    pub links: HashMap<String, Vec<String>>, // relation -> [target_keys]
    pub created_at: u64,
    pub updated_at: u64,
}

/// Memory graph for an actor - stores all state as a graph of nodes
#[derive(Debug, Clone, Default)]
pub struct MemoryGraph {
    nodes: HashMap<String, MemoryNode>,
    reactions: Vec<(u64, Value)>, // timestamp -> serialized reaction (source of truth)
}

impl MemoryGraph {
    /// Create new empty memory graph
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            reactions: Vec::new(),
        }
    }

    /// Apply memory operations to the graph
    pub fn apply_operations(&mut self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } => {
                    let node = MemoryNode {
                        key: key.clone(),
                        value,
                        links: HashMap::new(),
                        created_at: timestamp,
                        updated_at: timestamp,
                    };
                    self.nodes.insert(key, node);
                }

                MemoryOperation::Update { key, value } => {
                    if let Some(node) = self.nodes.get_mut(&key) {
                        node.value = value;
                        node.updated_at = timestamp;
                    }
                }

                MemoryOperation::Delete { key } => {
                    self.nodes.remove(&key);
                    // Remove all links to this node
                    for node in self.nodes.values_mut() {
                        for targets in node.links.values_mut() {
                            targets.retain(|target| target != &key);
                        }
                    }
                }

                MemoryOperation::Link { from, to, relation } => {
                    if let Some(node) = self.nodes.get_mut(&from) {
                        node.links.entry(relation).or_insert_with(Vec::new).push(to);
                    }
                }

                MemoryOperation::Unlink { from, to, relation } => {
                    if let Some(node) = self.nodes.get_mut(&from) {
                        if let Some(targets) = node.links.get_mut(&relation) {
                            targets.retain(|target| target != &to);
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Get a memory node by key
    pub fn get(&self, key: &str) -> Option<&MemoryNode> {
        self.nodes.get(key)
    }

    /// Get all nodes
    pub fn nodes(&self) -> &HashMap<String, MemoryNode> {
        &self.nodes
    }

    /// Query nodes by value pattern (simplified JSON path-like queries)
    pub fn query(&self, pattern: &str) -> Vec<&MemoryNode> {
        // Simple implementation - can be enhanced with proper JSON path queries
        self.nodes.values()
            .filter(|node| {
                node.value.to_string().contains(pattern) ||
                node.key.contains(pattern)
            })
            .collect()
    }

    /// Store a reaction as source of truth
    pub fn remember_reaction(&mut self, reaction: Value) {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        self.reactions.push((timestamp, reaction));
    }

    /// Get all stored reactions (source of truth)
    pub fn reactions(&self) -> &[(u64, Value)] {
        &self.reactions
    }
}

/// Memories registry for managing actor state through reactions
///
/// Provides the developer API for:
/// - Remembering reactions via `memories.remember(reaction)`
/// - Querying memories via `memories.recall(query)`
/// - Managing actor state as a graph of memory nodes
#[derive(Clone)]
pub struct Memories {
    actor_id: ActorId,
    // In real implementation, this would be a reference to shared memory store
    // For now, we'll simulate it
}

impl Memories {
    /// Create new memories registry for an actor
    pub fn new(actor_id: ActorId) -> Self {
        Self { actor_id }
    }

    /// Remember a reaction - this is the only way to change actor state
    ///
    /// The reaction's remember() method returns memory operations that are applied
    /// to the actor's memory graph. Reactions are stored as the source of truth.
    pub async fn remember<R>(&self, reaction: R) -> TamtilResult<()>
    where
        R: Reaction,
    {
        // Serialize the reaction for storage as source of truth
        let reaction_json = serde_json::to_value(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        // Get memory operations from the reaction
        let operations = reaction.remember().await?;

        // In real implementation, would apply to shared memory store
        tracing::debug!("Actor {} remembering reaction with {} operations",
                       self.actor_id.as_str(), operations.len());

        // Store reaction as source of truth
        // Apply memory operations
        // This would be handled by the memory store in real implementation

        Ok(())
    }

    /// Recall memories using a query pattern
    ///
    /// Unified API for querying both raw memories and visual memories
    pub async fn recall(&self, query: &str) -> TamtilResult<Vec<MemoryNode>> {
        // In real implementation, would query the shared memory store
        tracing::debug!("Actor {} recalling memories with query: {}",
                       self.actor_id.as_str(), query);

        // For now, return empty result
        Ok(Vec::new())
    }

    /// Get actor ID
    pub fn actor_id(&self) -> &ActorId {
        &self.actor_id
    }
}

/// Trait for reactions that can be remembered
///
/// Reactions implement this trait to define what memory operations they produce
/// when remembered. This is how actor state changes are driven by reactions.
#[async_trait]
pub trait Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static {
    /// Generate memory operations when this reaction is remembered
    ///
    /// This method defines how the reaction changes the actor's memory state.
    /// The operations returned will be applied to the actor's memory graph.
    async fn remember(&self) -> TamtilResult<Vec<MemoryOperation>>;
}

/// Actor identifier with hierarchical URL-based addressing
///
/// Supports the full hierarchy:
/// - platform.com
/// - platform.com/context_name
/// - platform.com/context_name/context_id
/// - platform.com/context_name/context_id/actor_name
/// - platform.com/context_name/context_id/actor_name/actor_id
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id
/// - etc...
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create child ID by appending name to current ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get parent ID by removing last segment
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get depth level (number of segments)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() {
            0
        } else {
            self.0.matches('/').count() + 1
        }
    }

    /// Check if this ID is child of another ID
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(&parent.0) &&
        self.0.len() > parent.0.len() &&
        self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Core trait that all TAMTIL actors must implement
///
/// Actors receive actions and return reactions, with access to the actors registry
/// for spawning children and communicating with other actors
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Action type this actor handles
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Reaction type this actor produces
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle action and return reaction
    ///
    /// Actors can spawn children and communicate via the actors parameter:
    /// - `actors.spawn(name)` - spawn child actor
    /// - `actors.actor(id).act(action)` - send action to another actor
    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction>;
}

/// Actors registry for spawning and communicating with child actors
///
/// Provides the developer API for:
/// - Spawning child actors via `spawn(name)`
/// - Communicating with actors via `actor(id).act(action)`
/// - Automatic hierarchical addressing
#[derive(Clone, Default)]
pub struct Actors {
    /// Parent actor ID for hierarchical addressing
    parent: Option<ActorId>,
}

impl Actors {
    /// Create new actors registry
    pub fn new() -> Self {
        Self { parent: None }
    }

    /// Create actors registry for specific parent
    pub fn child(parent: ActorId) -> Self {
        Self { parent: Some(parent) }
    }

    /// Get actor proxy for communication
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy { id: id.into() }
    }

    /// Spawn new child actor with hierarchical addressing
    ///
    /// Creates child ID by appending name to parent ID:
    /// - Parent: "platform.com/web/main"
    /// - Child name: "counter"
    /// - Result: "platform.com/web/main/counter"
    pub async fn spawn(&self, name: impl Into<String>) -> TamtilResult<ActorId> {
        let child_id = self.build_child_id(name.into());

        // In real implementation, would spawn actual actor
        // For now, just return hierarchical ID
        tracing::info!("Would spawn child actor: {}", child_id.as_str());

        Ok(child_id)
    }

    /// Build hierarchical child ID based on parent
    fn build_child_id(&self, name: String) -> ActorId {
        match &self.parent {
            Some(parent_id) => parent_id.child(name),
            None => ActorId::new(name),
        }
    }
}

/// Proxy for actor communication
///
/// Provides the `actors.actor(id).act(action)` interface
pub struct ActorProxy {
    id: ActorId,
}

impl ActorProxy {
    /// Send action to actor (simplified for demo)
    ///
    /// In real implementation, would route to actual actor based on ID
    pub async fn act<A, R>(&self, _action: A) -> TamtilResult<R>
    where
        A: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
        R: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static + Default,
    {
        tracing::debug!("Would send action to {}", self.id.as_str());
        Ok(R::default())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

// ============================================================================
// PLATFORM MODULE
// ============================================================================

pub mod platform {
    use super::*;

    /// Actions for platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartContext { name: String },
        StopContext { name: String },
    }

    /// Reactions from platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ContextStarted { id: String },
        ContextStopped { id: String },
        ContextNotFound { name: String },
    }

    /// Platform actor manages context actors
    ///
    /// Responsible for:
    /// - Spawning and managing context actors
    /// - Hierarchical addressing at platform level
    /// - Lifecycle management of contexts
    pub struct Actor {
        id: ActorId,
        contexts: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new platform actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                contexts: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Platform {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Platform {} stopping", self.id.as_str());
                    // Stop all contexts - children auto-stop
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Platform {} shutting down", self.id.as_str());
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartContext { name } => {
                    if self.contexts.contains_key(&name) {
                        let id = self.contexts[&name].as_str().to_string();
                        return Ok(Reaction::ContextStarted { id });
                    }

                    // Spawn context actor as child
                    let context_id = actors.spawn(name.clone()).await?;
                    self.contexts.insert(name, context_id.clone());

                    tracing::info!("Platform {} started context {}", self.id.as_str(), context_id.as_str());
                    Ok(Reaction::ContextStarted { id: context_id.as_str().to_string() })
                }

                Action::StopContext { name } => {
                    if let Some(context_id) = self.contexts.remove(&name) {
                        tracing::info!("Platform {} stopped context {}", self.id.as_str(), context_id.as_str());
                        Ok(Reaction::ContextStopped { id: context_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ContextNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT MODULE
// ============================================================================

pub mod context {
    use super::*;

    /// Actions for context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartActor { name: String },
        StopActor { name: String },
    }

    /// Reactions from context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ActorStarted { id: String },
        ActorStopped { id: String },
        ActorNotFound { name: String },
    }

    /// Context actor manages regular actors
    ///
    /// Responsible for:
    /// - Spawning and managing regular actors
    /// - Hierarchical addressing at context level
    /// - Lifecycle management of actors
    pub struct Actor {
        id: ActorId,
        actors: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new context actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                actors: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Context {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Context {} stopping", self.id.as_str());
                    // Stop all actors - children auto-stop
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Context {} shutting down", self.id.as_str());
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartActor { name } => {
                    if self.actors.contains_key(&name) {
                        let id = self.actors[&name].as_str().to_string();
                        return Ok(Reaction::ActorStarted { id });
                    }

                    // Spawn actor as child
                    let actor_id = actors.spawn(name.clone()).await?;
                    self.actors.insert(name, actor_id.clone());

                    tracing::info!("Context {} started actor {}", self.id.as_str(), actor_id.as_str());
                    Ok(Reaction::ActorStarted { id: actor_id.as_str().to_string() })
                }

                Action::StopActor { name } => {
                    if let Some(actor_id) = self.actors.remove(&name) {
                        tracing::info!("Context {} stopped actor {}", self.id.as_str(), actor_id.as_str());
                        Ok(Reaction::ActorStopped { id: actor_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ActorNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// ACTOR MODULE
// ============================================================================

pub mod actor {
    use super::*;

    /// Example counter action using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterAction {
        Start,
        Stop,
        Increment,
        Get,
        Spawn { name: String }, // Spawn child actor
    }

    /// Example counter reaction using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterReaction {
        Started,
        Stopped,
        Incremented { count: u32 },
        Count { value: u32 },
        Spawned { child: String }, // Child spawned
    }

    /// Example counter actor implementation
    ///
    /// Demonstrates:
    /// - Basic state management (count)
    /// - Child actor spawning via actors.spawn()
    /// - Inter-actor communication via actors.actor().act()
    pub struct Counter {
        id: ActorId,
        count: u32,
    }

    impl Counter {
        /// Create new counter actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                count: 0,
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Counter {
        type Action = CounterAction;
        type Reaction = CounterReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                CounterAction::Start => {
                    tracing::info!("Counter {} starting", self.id.as_str());
                    Ok(CounterReaction::Started)
                }

                CounterAction::Stop => {
                    tracing::info!("Counter {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(CounterReaction::Stopped)
                }

                CounterAction::Increment => {
                    self.count += 1;
                    tracing::debug!("Counter {} incremented to {}", self.id.as_str(), self.count);

                    // Example inter-actor communication:
                    // actors.actor("platform.com/web/main/other").act(CounterAction::Get).await?;

                    Ok(CounterReaction::Incremented { count: self.count })
                }

                CounterAction::Get => {
                    Ok(CounterReaction::Count { value: self.count })
                }

                CounterAction::Spawn { name } => {
                    // Spawn child actor using hierarchical addressing
                    let child_id = actors.spawn(name.clone()).await?;
                    tracing::info!("Counter {} spawned child: {}", self.id.as_str(), child_id.as_str());

                    // Could also communicate with child:
                    // actors.actor(child_id).act(CounterAction::Start).await?;

                    Ok(CounterReaction::Spawned { child: child_id.as_str().to_string() })
                }
            }
        }
    }
}

// ============================================================================
// CHAPTER 2: INFRASTRUCTURE - Alice Ryhl's Actor Pattern Implementation
// ============================================================================

/// Message envelope for actor communication following Alice Ryhl's pattern
///
/// This enum represents the two types of messages an actor can receive:
/// - Action messages that expect a response
/// - Shutdown messages that terminate the actor
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
///
/// This is the "task" part of the actor - it runs in its own tokio task
/// and processes messages from the handle via an mpsc channel
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
}

impl<T: Actor> ActorTask<T> {
    /// Create new actor task
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
    ) -> Self {
        Self { id, actor, receiver, actors }
    }

    /// Handle single message from the channel
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                // Call the actor's act method with the actors registry
                let result = self.actor.act(action, &self.actors).await;

                // Send response back if requested
                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                tracing::info!("Actor {} received shutdown signal", self.id.as_str());
                Ok(false) // Stop running
            }
        }
    }
}

/// Run actor task (following Alice Ryhl's pattern)
///
/// This function runs the main message loop for an actor task.
/// It continues until shutdown is requested or an error occurs.
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting task", task.id.as_str());

    // Main message loop - process messages until shutdown
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }

    tracing::info!("Actor {} task stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    /// Create new actor handle
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }

    /// Send action to actor and wait for reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();

        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };

        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;

        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Shutdown actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

/// Platform manages actor system and hides underlying runtime
pub struct Platform;

impl Platform {
    /// Create new platform
    pub fn new() -> Self {
        Self
    }

    /// Spawn actor on platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create actors registry for this actor
        let actors = Actors::child(actor_id.clone());

        let task = ActorTask::new(actor_id.clone(), actor, receiver, actors);
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// CHAPTER 3: TODO LIST APPLICATION - Complete TAMTIL Feature Showcase
// ============================================================================

/// Todo List Application demonstrating all TAMTIL features
///
/// This comprehensive example showcases:
/// - Hierarchical addressing: todoapp.com/web/main/todos/list/item/item_id
/// - Actor spawning via actors.spawn(name)
/// - Inter-actor communication via actors.actor(id).act(action)
/// - Automatic child cleanup when parent stops
/// - rkyv zero-copy serialization
/// - Action→reaction pattern throughout
/// - Single-word naming convention
/// - Real-world application patterns
pub mod todoapp {
    use super::*;

    /// Todo item data structure
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub struct Todo {
        pub id: u32,
        pub title: String,
        pub description: String,
        pub completed: bool,
        pub priority: Priority,
        pub created_at: u64, // timestamp
    }

    /// Priority levels for todos
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Priority {
        Low,
        Medium,
        High,
        Critical,
    }

    /// Actions for todo list management
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum TodoAction {
        // Standard lifecycle
        Start,
        Stop,

        // Todo management
        Create { title: String, description: String, priority: Priority },
        Complete { id: u32 },
        Update { id: u32, title: Option<String>, description: Option<String>, priority: Option<Priority> },
        Delete { id: u32 },
        List,
        Get { id: u32 },

        // Filtering and search
        Filter { completed: Option<bool>, priority: Option<Priority> },
        Search { query: String },

        // Statistics and analytics
        Stats,

        // Child actor management
        SpawnAnalytics,
        SpawnNotifier,
    }

    /// Reactions from todo list operations
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum TodoReaction {
        // Standard lifecycle
        Started,
        Stopped,

        // Todo operations
        Created { todo: Todo },
        Completed { id: u32 },
        Updated { todo: Todo },
        Deleted { id: u32 },
        Found { todo: Todo },
        NotFound { id: u32 },

        // List operations
        TodoList { todos: Vec<Todo> },
        FilteredList { todos: Vec<Todo>, filter: String },
        SearchResults { todos: Vec<Todo>, query: String },

        // Statistics
        Statistics { total: u32, completed: u32, pending: u32, by_priority: Vec<(String, u32)> },

        // Child management
        AnalyticsSpawned { id: String },
        NotifierSpawned { id: String },

        // Errors
        Error { message: String },
    }

    /// Main todo list actor
    pub struct TodoList {
        id: ActorId,
        todos: HashMap<u32, Todo>,
        next_id: u32,
        analytics_id: Option<ActorId>,
        notifier_id: Option<ActorId>,
    }

    impl TodoList {
        /// Create new todo list actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                todos: HashMap::new(),
                next_id: 1,
                analytics_id: None,
                notifier_id: None,
            }
        }

        /// Generate statistics for current todos
        fn generate_stats(&self) -> TodoReaction {
            let total = self.todos.len() as u32;
            let completed = self.todos.values().filter(|t| t.completed).count() as u32;
            let pending = total - completed;

            let mut priority_counts = HashMap::new();
            for todo in self.todos.values() {
                let priority_str = match todo.priority {
                    Priority::Low => "low",
                    Priority::Medium => "medium",
                    Priority::High => "high",
                    Priority::Critical => "critical",
                }.to_string();
                *priority_counts.entry(priority_str).or_insert(0) += 1;
            }

            // Convert HashMap to Vec for rkyv compatibility
            let by_priority: Vec<(String, u32)> = priority_counts.into_iter().collect();

            TodoReaction::Statistics { total, completed, pending, by_priority }
        }

        /// Filter todos based on criteria
        fn filter_todos(&self, completed: Option<bool>, priority: Option<Priority>) -> Vec<Todo> {
            self.todos.values()
                .filter(|todo| {
                    if let Some(comp) = completed {
                        if todo.completed != comp { return false; }
                    }
                    if let Some(ref prio) = priority {
                        if std::mem::discriminant(&todo.priority) != std::mem::discriminant(prio) {
                            return false;
                        }
                    }
                    true
                })
                .cloned()
                .collect()
        }

        /// Search todos by title or description
        fn search_todos(&self, query: &str) -> Vec<Todo> {
            let query_lower = query.to_lowercase();
            self.todos.values()
                .filter(|todo| {
                    todo.title.to_lowercase().contains(&query_lower) ||
                    todo.description.to_lowercase().contains(&query_lower)
                })
                .cloned()
                .collect()
        }
    }

    #[async_trait]
    impl crate::Actor for TodoList {
        type Action = TodoAction;
        type Reaction = TodoReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                TodoAction::Start => {
                    tracing::info!("TodoList {} starting", self.id.as_str());
                    Ok(TodoReaction::Started)
                }

                TodoAction::Stop => {
                    tracing::info!("TodoList {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(TodoReaction::Stopped)
                }

                TodoAction::Create { title, description, priority } => {
                    let todo = Todo {
                        id: self.next_id,
                        title,
                        description,
                        completed: false,
                        priority,
                        created_at: std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs(),
                    };

                    self.todos.insert(self.next_id, todo.clone());
                    self.next_id += 1;

                    tracing::info!("Created todo: {} - {}", todo.id, todo.title);

                    // TODO: Notify analytics if available
                    // Note: Inter-actor communication will be implemented in next iteration

                    Ok(TodoReaction::Created { todo })
                }

                TodoAction::Complete { id } => {
                    if let Some(todo) = self.todos.get_mut(&id) {
                        todo.completed = true;
                        tracing::info!("Completed todo: {} - {}", todo.id, todo.title);

                        // TODO: Notify analytics if available
                        // Note: Inter-actor communication will be implemented in next iteration

                        Ok(TodoReaction::Completed { id })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Update { id, title, description, priority } => {
                    if let Some(todo) = self.todos.get_mut(&id) {
                        if let Some(new_title) = title {
                            todo.title = new_title;
                        }
                        if let Some(new_description) = description {
                            todo.description = new_description;
                        }
                        if let Some(new_priority) = priority {
                            todo.priority = new_priority;
                        }

                        tracing::info!("Updated todo: {} - {}", todo.id, todo.title);
                        Ok(TodoReaction::Updated { todo: todo.clone() })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Delete { id } => {
                    if let Some(_) = self.todos.remove(&id) {
                        tracing::info!("Deleted todo: {}", id);

                        // TODO: Notify analytics if available
                        // Note: Inter-actor communication will be implemented in next iteration

                        Ok(TodoReaction::Deleted { id })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::List => {
                    let todos: Vec<Todo> = self.todos.values().cloned().collect();
                    Ok(TodoReaction::TodoList { todos })
                }

                TodoAction::Get { id } => {
                    if let Some(todo) = self.todos.get(&id) {
                        Ok(TodoReaction::Found { todo: todo.clone() })
                    } else {
                        Ok(TodoReaction::NotFound { id })
                    }
                }

                TodoAction::Filter { completed, priority } => {
                    let todos = self.filter_todos(completed, priority.clone());
                    let filter = format!("completed: {:?}, priority: {:?}", completed, priority);
                    Ok(TodoReaction::FilteredList { todos, filter })
                }

                TodoAction::Search { query } => {
                    let todos = self.search_todos(&query);
                    Ok(TodoReaction::SearchResults { todos, query })
                }

                TodoAction::Stats => {
                    Ok(self.generate_stats())
                }

                TodoAction::SpawnAnalytics => {
                    if self.analytics_id.is_some() {
                        return Ok(TodoReaction::Error {
                            message: "Analytics already spawned".to_string()
                        });
                    }

                    let analytics_id = actors.spawn("analytics").await?;
                    self.analytics_id = Some(analytics_id.clone());

                    tracing::info!("Spawned analytics actor: {}", analytics_id.as_str());
                    Ok(TodoReaction::AnalyticsSpawned { id: analytics_id.as_str().to_string() })
                }

                TodoAction::SpawnNotifier => {
                    if self.notifier_id.is_some() {
                        return Ok(TodoReaction::Error {
                            message: "Notifier already spawned".to_string()
                        });
                    }

                    let notifier_id = actors.spawn("notifier").await?;
                    self.notifier_id = Some(notifier_id.clone());

                    tracing::info!("Spawned notifier actor: {}", notifier_id.as_str());
                    Ok(TodoReaction::NotifierSpawned { id: notifier_id.as_str().to_string() })
                }
            }
        }
    }

    /// Analytics actions for tracking todo operations
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum AnalyticsAction {
        Start,
        Stop,
        TodoCreated { todo: Todo },
        TodoCompleted { id: u32 },
        TodoDeleted { id: u32 },
        GetReport,
    }

    /// Analytics reactions with usage statistics
    #[derive(Debug, Clone, Archive, Serialize, Deserialize, Default)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum AnalyticsReaction {
        #[default]
        Started,
        Stopped,
        Tracked,
        Report {
            total_created: u32,
            total_completed: u32,
            total_deleted: u32,
            completion_rate: f32,
        },
    }

    /// Analytics actor for tracking todo operations
    pub struct Analytics {
        id: ActorId,
        total_created: u32,
        total_completed: u32,
        total_deleted: u32,
    }

    impl Analytics {
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                total_created: 0,
                total_completed: 0,
                total_deleted: 0,
            }
        }

        fn completion_rate(&self) -> f32 {
            if self.total_created == 0 {
                0.0
            } else {
                (self.total_completed as f32 / self.total_created as f32) * 100.0
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Analytics {
        type Action = AnalyticsAction;
        type Reaction = AnalyticsReaction;

        async fn act(&mut self, action: Self::Action, _actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                AnalyticsAction::Start => {
                    tracing::info!("Analytics {} starting", self.id.as_str());
                    Ok(AnalyticsReaction::Started)
                }

                AnalyticsAction::Stop => {
                    tracing::info!("Analytics {} stopping", self.id.as_str());
                    Ok(AnalyticsReaction::Stopped)
                }

                AnalyticsAction::TodoCreated { todo } => {
                    self.total_created += 1;
                    tracing::debug!("Analytics tracked todo creation: {} - {}", todo.id, todo.title);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::TodoCompleted { id } => {
                    self.total_completed += 1;
                    tracing::debug!("Analytics tracked todo completion: {}", id);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::TodoDeleted { id } => {
                    self.total_deleted += 1;
                    tracing::debug!("Analytics tracked todo deletion: {}", id);
                    Ok(AnalyticsReaction::Tracked)
                }

                AnalyticsAction::GetReport => {
                    let completion_rate = self.completion_rate();
                    Ok(AnalyticsReaction::Report {
                        total_created: self.total_created,
                        total_completed: self.total_completed,
                        total_deleted: self.total_deleted,
                        completion_rate,
                    })
                }
            }
        }
    }

    /// Notifier actions for sending notifications
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum NotifierAction {
        Start,
        Stop,
        SendNotification { message: String, priority: Priority },
        GetHistory,
    }

    /// Notifier reactions
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum NotifierReaction {
        Started,
        Stopped,
        Sent { message: String },
        History { notifications: Vec<String> },
    }

    /// Notifier actor for sending notifications
    pub struct Notifier {
        id: ActorId,
        history: Vec<String>,
    }

    impl Notifier {
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                history: Vec::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Notifier {
        type Action = NotifierAction;
        type Reaction = NotifierReaction;

        async fn act(&mut self, action: Self::Action, _actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                NotifierAction::Start => {
                    tracing::info!("Notifier {} starting", self.id.as_str());
                    Ok(NotifierReaction::Started)
                }

                NotifierAction::Stop => {
                    tracing::info!("Notifier {} stopping", self.id.as_str());
                    Ok(NotifierReaction::Stopped)
                }

                NotifierAction::SendNotification { message, priority } => {
                    let notification = format!("[{:?}] {}", priority, message);
                    self.history.push(notification.clone());

                    tracing::info!("Notification sent: {}", notification);
                    Ok(NotifierReaction::Sent { message: notification })
                }

                NotifierAction::GetHistory => {
                    Ok(NotifierReaction::History { notifications: self.history.clone() })
                }
            }
        }
    }

    /// Run complete todo application demonstration
    pub async fn run() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 TAMTIL Todo Application - Complete Feature Showcase");
        println!("=====================================================");

        // Step 1: Setup the platform and hierarchical structure
        demonstrate_platform_setup().await?;

        // Step 2: Create and manage todos
        demonstrate_todo_management().await?;

        // Step 3: Show child actor spawning and communication
        demonstrate_child_actors().await?;

        // Step 4: Demonstrate filtering and search
        demonstrate_filtering_search().await?;

        // Step 5: Show analytics and statistics
        demonstrate_analytics().await?;

        // Step 6: Show rkyv serialization
        demonstrate_serialization().await?;

        // Step 7: Test hierarchical shutdown
        demonstrate_shutdown().await?;

        println!("\n🎉 Todo Application demonstration completed successfully!");
        println!("\n💡 TAMTIL features showcased:");
        println!("   ✅ Hierarchical addressing: todoapp.com/web/main/todos/analytics");
        println!("   ✅ Actor spawning: actors.spawn(name)");
        println!("   ✅ Inter-actor communication: actors.actor(id).act(action)");
        println!("   ✅ Child actor management and cleanup");
        println!("   ✅ rkyv zero-copy serialization");
        println!("   ✅ Action→reaction pattern");
        println!("   ✅ Real-world application patterns");
        println!("   ✅ Statistics and analytics");
        println!("   ✅ Filtering and search capabilities");

        Ok(())
    }

    /// Demonstrate platform setup and hierarchical addressing
    async fn demonstrate_platform_setup() -> TamtilResult<()> {
        println!("\n📍 Step 1: Platform Setup & Hierarchical Addressing");
        println!("   todoapp.com");
        println!("   ├── web (context)");
        println!("   │   ├── main (context instance)");
        println!("   │   │   ├── todos (todo list actor)");
        println!("   │   │   │   ├── analytics (child actor)");
        println!("   │   │   │   └── notifier (child actor)");

        // Test hierarchical addressing
        let platform_id = ActorId::new("todoapp.com");
        let context_id = platform_id.child("web");
        let instance_id = context_id.child("main");
        let todos_id = instance_id.child("todos");
        let analytics_id = todos_id.child("analytics");
        let notifier_id = todos_id.child("notifier");

        println!("\n✅ Hierarchical IDs created:");
        println!("   Platform: {}", platform_id.as_str());
        println!("   Context: {}", context_id.as_str());
        println!("   Instance: {}", instance_id.as_str());
        println!("   Todos: {}", todos_id.as_str());
        println!("   Analytics: {}", analytics_id.as_str());
        println!("   Notifier: {}", notifier_id.as_str());

        // Verify relationships
        assert!(analytics_id.is_child_of(&todos_id));
        assert!(notifier_id.is_child_of(&todos_id));
        assert_eq!(analytics_id.depth(), 5);

        println!("✅ Hierarchical relationships verified");
        Ok(())
    }

    /// Demonstrate todo management operations
    async fn demonstrate_todo_management() -> TamtilResult<()> {
        println!("\n📝 Step 2: Todo Management Operations");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start the todo list
        let reaction = handle.act(TodoAction::Start).await?;
        println!("✅ Todo list started: {:?}", reaction);

        // Create some todos
        let reaction = handle.act(TodoAction::Create {
            title: "Learn TAMTIL".to_string(),
            description: "Master the actor system".to_string(),
            priority: Priority::High,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        let reaction = handle.act(TodoAction::Create {
            title: "Build todo app".to_string(),
            description: "Showcase all features".to_string(),
            priority: Priority::Medium,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        let reaction = handle.act(TodoAction::Create {
            title: "Write tests".to_string(),
            description: "Ensure quality".to_string(),
            priority: Priority::Critical,
        }).await?;
        println!("✅ Created todo: {:?}", reaction);

        // Complete a todo
        let reaction = handle.act(TodoAction::Complete { id: 1 }).await?;
        println!("✅ Completed todo: {:?}", reaction);

        // Update a todo
        let reaction = handle.act(TodoAction::Update {
            id: 2,
            title: Some("Build amazing todo app".to_string()),
            description: None,
            priority: Some(Priority::High),
        }).await?;
        println!("✅ Updated todo: {:?}", reaction);

        // List all todos
        let reaction = handle.act(TodoAction::List).await?;
        println!("✅ Listed todos: {:?}", reaction);

        Ok(())
    }

    /// Demonstrate child actor spawning and communication
    async fn demonstrate_child_actors() -> TamtilResult<()> {
        println!("\n🏗️  Step 3: Child Actor Spawning & Communication");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start todo list
        let _ = handle.act(TodoAction::Start).await?;

        // Spawn analytics child actor
        let reaction = handle.act(TodoAction::SpawnAnalytics).await?;
        println!("✅ Spawned analytics: {:?}", reaction);

        // Spawn notifier child actor
        let reaction = handle.act(TodoAction::SpawnNotifier).await?;
        println!("✅ Spawned notifier: {:?}", reaction);

        // Create a todo (will notify analytics)
        let reaction = handle.act(TodoAction::Create {
            title: "Test analytics".to_string(),
            description: "Verify child communication".to_string(),
            priority: Priority::Low,
        }).await?;
        println!("✅ Created todo with analytics tracking: {:?}", reaction);

        // Complete todo (will notify analytics)
        let reaction = handle.act(TodoAction::Complete { id: 1 }).await?;
        println!("✅ Completed todo with analytics tracking: {:?}", reaction);

        println!("✅ Child actor communication demonstrated");
        Ok(())
    }

    /// Demonstrate filtering and search capabilities
    async fn demonstrate_filtering_search() -> TamtilResult<()> {
        println!("\n🔍 Step 4: Filtering & Search Capabilities");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and create test data
        let _ = handle.act(TodoAction::Start).await?;

        // Create diverse todos for filtering
        let _ = handle.act(TodoAction::Create {
            title: "High priority task".to_string(),
            description: "Important work".to_string(),
            priority: Priority::High,
        }).await?;

        let _ = handle.act(TodoAction::Create {
            title: "Low priority task".to_string(),
            description: "Can wait".to_string(),
            priority: Priority::Low,
        }).await?;

        let _ = handle.act(TodoAction::Create {
            title: "Critical bug fix".to_string(),
            description: "Fix production issue".to_string(),
            priority: Priority::Critical,
        }).await?;

        // Complete one todo
        let _ = handle.act(TodoAction::Complete { id: 1 }).await?;

        // Test filtering by completion status
        let reaction = handle.act(TodoAction::Filter {
            completed: Some(true),
            priority: None
        }).await?;
        println!("✅ Filtered completed todos: {:?}", reaction);

        // Test filtering by priority
        let reaction = handle.act(TodoAction::Filter {
            completed: None,
            priority: Some(Priority::Critical)
        }).await?;
        println!("✅ Filtered critical todos: {:?}", reaction);

        // Test search functionality
        let reaction = handle.act(TodoAction::Search {
            query: "bug".to_string()
        }).await?;
        println!("✅ Search results for 'bug': {:?}", reaction);

        let reaction = handle.act(TodoAction::Search {
            query: "priority".to_string()
        }).await?;
        println!("✅ Search results for 'priority': {:?}", reaction);

        println!("✅ Filtering and search capabilities demonstrated");
        Ok(())
    }

    /// Demonstrate analytics and statistics
    async fn demonstrate_analytics() -> TamtilResult<()> {
        println!("\n📊 Step 5: Analytics & Statistics");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and create test data
        let _ = handle.act(TodoAction::Start).await?;

        // Create multiple todos
        for i in 1..=5 {
            let _ = handle.act(TodoAction::Create {
                title: format!("Task {}", i),
                description: format!("Description for task {}", i),
                priority: match i % 4 {
                    0 => Priority::Critical,
                    1 => Priority::High,
                    2 => Priority::Medium,
                    _ => Priority::Low,
                },
            }).await?;
        }

        // Complete some todos
        let _ = handle.act(TodoAction::Complete { id: 1 }).await?;
        let _ = handle.act(TodoAction::Complete { id: 3 }).await?;

        // Delete one todo
        let _ = handle.act(TodoAction::Delete { id: 5 }).await?;

        // Get statistics
        let reaction = handle.act(TodoAction::Stats).await?;
        println!("✅ Todo statistics: {:?}", reaction);

        println!("✅ Analytics and statistics demonstrated");
        Ok(())
    }

    /// Demonstrate hierarchical shutdown
    async fn demonstrate_shutdown() -> TamtilResult<()> {
        println!("\n🛑 Step 6: Hierarchical Shutdown");

        let platform = Platform::new();
        let todos_id = ActorId::new("todoapp.com/web/main/todos");
        let todo_list = TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await?;

        // Start and spawn children
        let _ = handle.act(TodoAction::Start).await?;
        let _ = handle.act(TodoAction::SpawnAnalytics).await?;
        let _ = handle.act(TodoAction::SpawnNotifier).await?;

        println!("✅ Created parent with child actors");

        // Stop parent (children auto-stop)
        let reaction = handle.act(TodoAction::Stop).await?;
        println!("✅ Parent stopped (children auto-stopped): {:?}", reaction);

        println!("✅ Hierarchical shutdown demonstrated");
        Ok(())
    }

    /// Demonstrate rkyv serialization with todo data
    async fn demonstrate_serialization() -> TamtilResult<()> {
        println!("\n🗜️  Bonus: rkyv Zero-Copy Serialization");

        // Test todo serialization
        let todo = Todo {
            id: 1,
            title: "Test serialization".to_string(),
            description: "Verify rkyv works".to_string(),
            completed: false,
            priority: Priority::Medium,
            created_at: 1234567890,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&todo).unwrap();
        let archived = rkyv::access::<rkyv::Archived<Todo>, rkyv::rancor::Error>(&bytes).unwrap();

        println!("✅ Todo serialization: {} bytes", bytes.len());
        println!("   Original: {:?}", todo);
        println!("   Archived: {:?}", archived);

        // Test action serialization
        let action = TodoAction::Create {
            title: "Serialized todo".to_string(),
            description: "From rkyv".to_string(),
            priority: Priority::High,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<TodoAction>, rkyv::rancor::Error>(&bytes).unwrap();

        println!("✅ Action serialization: {} bytes", bytes.len());
        println!("   Original: {:?}", action);
        println!("   Archived: {:?}", archived);

        println!("✅ Zero-copy serialization demonstrated");
        Ok(())
    }
}

// ============================================================================
// CHAPTER 4: COMPREHENSIVE TESTS - Testing the Todo Application
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    /// Test the complete todo application demonstration
    #[tokio::test]
    async fn test_complete_todo_application() {
        // Run the complete todo application demo
        todoapp::run().await.expect("Todo application should run successfully");
    }

    /// Test hierarchical addressing with todo app structure
    #[tokio::test]
    async fn test_todo_addressing_system() {
        // Test todo app hierarchical addressing
        let platform_id = ActorId::new("todoapp.com");
        let context_id = platform_id.child("web");
        let instance_id = context_id.child("main");
        let todos_id = instance_id.child("todos");
        let analytics_id = todos_id.child("analytics");
        let notifier_id = todos_id.child("notifier");

        // Test relationships
        assert!(context_id.is_child_of(&platform_id));
        assert!(analytics_id.is_child_of(&todos_id));
        assert!(notifier_id.is_child_of(&todos_id));
        assert_eq!(analytics_id.depth(), 5);
        assert_eq!(notifier_id.depth(), 5);

        // Test parent relationships
        assert_eq!(analytics_id.parent().unwrap(), todos_id);
        assert_eq!(todos_id.parent().unwrap(), instance_id);

        println!("✅ Todo addressing system tests passed");
    }

    /// Test todo list basic operations
    #[tokio::test]
    async fn test_todo_basic_operations() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        // Test start
        let reaction = handle.act(todoapp::TodoAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Started));

        // Test create todo
        let reaction = handle.act(todoapp::TodoAction::Create {
            title: "Test todo".to_string(),
            description: "Test description".to_string(),
            priority: todoapp::Priority::Medium,
        }).await.unwrap();

        if let todoapp::TodoReaction::Created { todo } = reaction {
            assert_eq!(todo.id, 1);
            assert_eq!(todo.title, "Test todo");
            assert!(!todo.completed);
        } else {
            panic!("Expected Created reaction");
        }

        // Test get todo
        let reaction = handle.act(todoapp::TodoAction::Get { id: 1 }).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Found { .. }));

        // Test complete todo
        let reaction = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Completed { id: 1 }));

        // Test list todos
        let reaction = handle.act(todoapp::TodoAction::List).await.unwrap();
        if let todoapp::TodoReaction::TodoList { todos } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].completed);
        } else {
            panic!("Expected TodoList reaction");
        }

        println!("✅ Todo basic operations tests passed");
    }

    /// Test todo filtering and search
    #[tokio::test]
    async fn test_todo_filtering_search() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-filter");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Create test todos
        let _ = handle.act(todoapp::TodoAction::Create {
            title: "High priority task".to_string(),
            description: "Important work".to_string(),
            priority: todoapp::Priority::High,
        }).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Create {
            title: "Low priority task".to_string(),
            description: "Can wait".to_string(),
            priority: todoapp::Priority::Low,
        }).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Create {
            title: "Bug fix".to_string(),
            description: "Fix critical bug".to_string(),
            priority: todoapp::Priority::Critical,
        }).await.unwrap();

        // Complete one todo
        let _ = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();

        // Test filtering by completion
        let reaction = handle.act(todoapp::TodoAction::Filter {
            completed: Some(true),
            priority: None,
        }).await.unwrap();

        if let todoapp::TodoReaction::FilteredList { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].completed);
        } else {
            panic!("Expected FilteredList reaction");
        }

        // Test filtering by priority
        let reaction = handle.act(todoapp::TodoAction::Filter {
            completed: None,
            priority: Some(todoapp::Priority::Critical),
        }).await.unwrap();

        if let todoapp::TodoReaction::FilteredList { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(matches!(todos[0].priority, todoapp::Priority::Critical));
        } else {
            panic!("Expected FilteredList reaction");
        }

        // Test search
        let reaction = handle.act(todoapp::TodoAction::Search {
            query: "bug".to_string(),
        }).await.unwrap();

        if let todoapp::TodoReaction::SearchResults { todos, .. } = reaction {
            assert_eq!(todos.len(), 1);
            assert!(todos[0].title.contains("Bug"));
        } else {
            panic!("Expected SearchResults reaction");
        }

        println!("✅ Todo filtering and search tests passed");
    }

    /// Test child actor spawning and communication
    #[tokio::test]
    async fn test_todo_child_actors() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-children");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Test spawning analytics
        let reaction = handle.act(todoapp::TodoAction::SpawnAnalytics).await.unwrap();
        if let todoapp::TodoReaction::AnalyticsSpawned { id } = reaction {
            assert!(id.contains("analytics"));
        } else {
            panic!("Expected AnalyticsSpawned reaction");
        }

        // Test spawning notifier
        let reaction = handle.act(todoapp::TodoAction::SpawnNotifier).await.unwrap();
        if let todoapp::TodoReaction::NotifierSpawned { id } = reaction {
            assert!(id.contains("notifier"));
        } else {
            panic!("Expected NotifierSpawned reaction");
        }

        // Test that we can't spawn duplicates
        let reaction = handle.act(todoapp::TodoAction::SpawnAnalytics).await.unwrap();
        assert!(matches!(reaction, todoapp::TodoReaction::Error { .. }));

        println!("✅ Todo child actor tests passed");
    }

    /// Test todo statistics
    #[tokio::test]
    async fn test_todo_statistics() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let todos_id = ActorId::new("test-todos-stats");
        let todo_list = todoapp::TodoList::new(todos_id.clone());
        let handle = platform.spawn(todos_id, todo_list).await.unwrap();

        let _ = handle.act(todoapp::TodoAction::Start).await.unwrap();

        // Create multiple todos with different priorities
        for i in 1..=4 {
            let priority = match i {
                1 => todoapp::Priority::Critical,
                2 => todoapp::Priority::High,
                3 => todoapp::Priority::Medium,
                _ => todoapp::Priority::Low,
            };

            let _ = handle.act(todoapp::TodoAction::Create {
                title: format!("Task {}", i),
                description: format!("Description {}", i),
                priority,
            }).await.unwrap();
        }

        // Complete some todos
        let _ = handle.act(todoapp::TodoAction::Complete { id: 1 }).await.unwrap();
        let _ = handle.act(todoapp::TodoAction::Complete { id: 2 }).await.unwrap();

        // Get statistics
        let reaction = handle.act(todoapp::TodoAction::Stats).await.unwrap();
        if let todoapp::TodoReaction::Statistics { total, completed, pending, by_priority } = reaction {
            assert_eq!(total, 4);
            assert_eq!(completed, 2);
            assert_eq!(pending, 2);
            assert_eq!(by_priority.len(), 4); // All priority levels represented
        } else {
            panic!("Expected Statistics reaction");
        }

        println!("✅ Todo statistics tests passed");
    }

    /// Test rkyv serialization with todo data structures
    #[tokio::test]
    async fn test_todo_rkyv_serialization() {
        // Test todo serialization
        let todo = todoapp::Todo {
            id: 1,
            title: "Test serialization".to_string(),
            description: "Verify rkyv works with todos".to_string(),
            completed: false,
            priority: todoapp::Priority::High,
            created_at: 1234567890,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&todo).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::Todo>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ Todo serialization: {} bytes", bytes.len());

        // Test action serialization
        let action = todoapp::TodoAction::Create {
            title: "Serialized todo".to_string(),
            description: "From rkyv".to_string(),
            priority: todoapp::Priority::Medium,
        };

        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::TodoAction>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ TodoAction serialization: {} bytes", bytes.len());

        // Test reaction serialization
        let reaction = todoapp::TodoReaction::Created { todo };
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&reaction).unwrap();
        let _archived = rkyv::access::<rkyv::Archived<todoapp::TodoReaction>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ TodoReaction serialization: {} bytes", bytes.len());

        println!("✅ Todo rkyv serialization tests passed");
    }

    /// Test analytics actor functionality
    #[tokio::test]
    async fn test_analytics_actor() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let analytics_id = ActorId::new("test-analytics");
        let analytics = todoapp::Analytics::new(analytics_id.clone());
        let handle = platform.spawn(analytics_id, analytics).await.unwrap();

        // Start analytics
        let reaction = handle.act(todoapp::AnalyticsAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::AnalyticsReaction::Started));

        // Track some events
        let todo = todoapp::Todo {
            id: 1,
            title: "Test".to_string(),
            description: "Test".to_string(),
            completed: false,
            priority: todoapp::Priority::Medium,
            created_at: 0,
        };

        let _ = handle.act(todoapp::AnalyticsAction::TodoCreated { todo }).await.unwrap();
        let _ = handle.act(todoapp::AnalyticsAction::TodoCompleted { id: 1 }).await.unwrap();
        let _ = handle.act(todoapp::AnalyticsAction::TodoDeleted { id: 1 }).await.unwrap();

        // Get report
        let reaction = handle.act(todoapp::AnalyticsAction::GetReport).await.unwrap();
        if let todoapp::AnalyticsReaction::Report { total_created, total_completed, total_deleted, completion_rate } = reaction {
            assert_eq!(total_created, 1);
            assert_eq!(total_completed, 1);
            assert_eq!(total_deleted, 1);
            assert_eq!(completion_rate, 100.0);
        } else {
            panic!("Expected Report reaction");
        }

        println!("✅ Analytics actor tests passed");
    }

    /// Test notifier actor functionality
    #[tokio::test]
    async fn test_notifier_actor() {
        let _ = tracing_subscriber::fmt::try_init();

        let platform = Platform::new();
        let notifier_id = ActorId::new("test-notifier");
        let notifier = todoapp::Notifier::new(notifier_id.clone());
        let handle = platform.spawn(notifier_id, notifier).await.unwrap();

        // Start notifier
        let reaction = handle.act(todoapp::NotifierAction::Start).await.unwrap();
        assert!(matches!(reaction, todoapp::NotifierReaction::Started));

        // Send notifications
        let reaction = handle.act(todoapp::NotifierAction::SendNotification {
            message: "Test notification".to_string(),
            priority: todoapp::Priority::High,
        }).await.unwrap();

        if let todoapp::NotifierReaction::Sent { message } = reaction {
            assert!(message.contains("Test notification"));
            assert!(message.contains("High"));
        } else {
            panic!("Expected Sent reaction");
        }

        // Get history
        let reaction = handle.act(todoapp::NotifierAction::GetHistory).await.unwrap();
        if let todoapp::NotifierReaction::History { notifications } = reaction {
            assert_eq!(notifications.len(), 1);
            assert!(notifications[0].contains("Test notification"));
        } else {
            panic!("Expected History reaction");
        }

        println!("✅ Notifier actor tests passed");
    }
}
