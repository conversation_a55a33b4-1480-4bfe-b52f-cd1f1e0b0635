//! # TAMTIL - The Actor Model That Is Like
//!
//! A high-performance actor system with rkyv zero-copy serialization, designed like an opera
//! where components work independently but in harmony.
//!
//! ## Table of Contents
//!
//! 1. [Core Concepts](#core-concepts)
//! 2. [Architecture](#architecture)
//! 3. [Addressing System](#addressing-system)
//! 4. [Developer API](#developer-api)
//! 5. [Platform Module](#platform-module)
//! 6. [Context Module](#context-module)
//! 7. [Actor Module](#actor-module)
//! 8. [Infrastructure](#infrastructure)
//! 9. [Complete Example](#complete-example)
//! 10. [Tests](#tests)
//!
//! ## Core Concepts
//!
//! Everything is an actor following the universal pattern: `actor.act(action, actors)`
//!
//! ### Key Features
//! - **Alice <PERSON>'s actor pattern** with tokio hidden behind platform abstraction
//! - **rkyv zero-copy serialization** for maximum performance
//! - **Single-word naming** convention throughout (unless impossible)
//! - **Hierarchical actor spawning** via `actors.spawn(name)`
//! - **URL-based addressing** with full hierarchy support
//! - **Action→reaction pattern** for all interactions
//! - **No locking** - actors handle concurrency elegantly
//! - **Automatic child cleanup** when parent stops
//!
//! ## Architecture
//!
//! ```text
//! Platform (platform.com)
//! └── Context (platform.com/web)
//!     └── Context Instance (platform.com/web/main)
//!         └── Actor (platform.com/web/main/counter)
//!             └── Actor Instance (platform.com/web/main/counter/stats)
//!                 └── Child Actor (platform.com/web/main/counter/stats/session)
//!                     └── Child Instance (platform.com/web/main/counter/stats/session/user123)
//! ```
//!
//! ## Addressing System
//!
//! TAMTIL supports hierarchical URL-based addressing up to 6+ levels:
//! - `platform.com`
//! - `platform.com/context_name`
//! - `platform.com/context_name/context_id`
//! - `platform.com/context_name/context_id/actor_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name`
//! - `platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id`
//! - And so on...
//!
//! ## Developer API
//!
//! Primary interactions:
//! - `actors.spawn(name)` - spawn child actor
//! - `actors.actor(id).act(action)` - send action to actor
//! - All actors receive `actors` parameter for spawning and communication

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;

// ============================================================================
// CHAPTER 1: CORE TYPES AND TRAITS
// ============================================================================

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
    #[error("Invalid address format: {address}")]
    InvalidAddress { address: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor identifier with hierarchical URL-based addressing
///
/// Supports the full hierarchy:
/// - platform.com
/// - platform.com/context_name
/// - platform.com/context_name/context_id
/// - platform.com/context_name/context_id/actor_name
/// - platform.com/context_name/context_id/actor_name/actor_id
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id
/// - etc...
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create child ID by appending name to current ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get parent ID by removing last segment
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get depth level (number of segments)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() {
            0
        } else {
            self.0.matches('/').count() + 1
        }
    }

    /// Check if this ID is child of another ID
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(&parent.0) &&
        self.0.len() > parent.0.len() &&
        self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Core trait that all TAMTIL actors must implement
///
/// Actors receive actions and return reactions, with access to the actors registry
/// for spawning children and communicating with other actors
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Action type this actor handles
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Reaction type this actor produces
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle action and return reaction
    ///
    /// Actors can spawn children and communicate via the actors parameter:
    /// - `actors.spawn(name)` - spawn child actor
    /// - `actors.actor(id).act(action)` - send action to another actor
    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction>;
}

/// Actors registry for spawning and communicating with child actors
///
/// Provides the developer API for:
/// - Spawning child actors via `spawn(name)`
/// - Communicating with actors via `actor(id).act(action)`
/// - Automatic hierarchical addressing
#[derive(Clone, Default)]
pub struct Actors {
    /// Parent actor ID for hierarchical addressing
    parent: Option<ActorId>,
}

impl Actors {
    /// Create new actors registry
    pub fn new() -> Self {
        Self { parent: None }
    }

    /// Create actors registry for specific parent
    pub fn child(parent: ActorId) -> Self {
        Self { parent: Some(parent) }
    }

    /// Get actor proxy for communication
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy { id: id.into() }
    }

    /// Spawn new child actor with hierarchical addressing
    ///
    /// Creates child ID by appending name to parent ID:
    /// - Parent: "platform.com/web/main"
    /// - Child name: "counter"
    /// - Result: "platform.com/web/main/counter"
    pub async fn spawn(&self, name: impl Into<String>) -> TamtilResult<ActorId> {
        let child_id = self.build_child_id(name.into());

        // In real implementation, would spawn actual actor
        // For now, just return hierarchical ID
        tracing::info!("Would spawn child actor: {}", child_id.as_str());

        Ok(child_id)
    }

    /// Build hierarchical child ID based on parent
    fn build_child_id(&self, name: String) -> ActorId {
        match &self.parent {
            Some(parent_id) => parent_id.child(name),
            None => ActorId::new(name),
        }
    }
}

/// Proxy for actor communication
///
/// Provides the `actors.actor(id).act(action)` interface
pub struct ActorProxy {
    id: ActorId,
}

impl ActorProxy {
    /// Send action to actor (simplified for demo)
    ///
    /// In real implementation, would route to actual actor based on ID
    pub async fn act<A, R>(&self, _action: A) -> TamtilResult<R>
    where
        A: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
        R: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static + Default,
    {
        tracing::debug!("Would send action to {}", self.id.as_str());
        Ok(R::default())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

// ============================================================================
// PLATFORM MODULE
// ============================================================================

pub mod platform {
    use super::*;

    /// Actions for platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartContext { name: String },
        StopContext { name: String },
    }

    /// Reactions from platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ContextStarted { id: String },
        ContextStopped { id: String },
        ContextNotFound { name: String },
    }

    /// Platform actor manages context actors
    ///
    /// Responsible for:
    /// - Spawning and managing context actors
    /// - Hierarchical addressing at platform level
    /// - Lifecycle management of contexts
    pub struct Actor {
        id: ActorId,
        contexts: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new platform actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                contexts: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Platform {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Platform {} stopping", self.id.as_str());
                    // Stop all contexts - children auto-stop
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Platform {} shutting down", self.id.as_str());
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartContext { name } => {
                    if self.contexts.contains_key(&name) {
                        let id = self.contexts[&name].as_str().to_string();
                        return Ok(Reaction::ContextStarted { id });
                    }

                    // Spawn context actor as child
                    let context_id = actors.spawn(name.clone()).await?;
                    self.contexts.insert(name, context_id.clone());

                    tracing::info!("Platform {} started context {}", self.id.as_str(), context_id.as_str());
                    Ok(Reaction::ContextStarted { id: context_id.as_str().to_string() })
                }

                Action::StopContext { name } => {
                    if let Some(context_id) = self.contexts.remove(&name) {
                        tracing::info!("Platform {} stopped context {}", self.id.as_str(), context_id.as_str());
                        Ok(Reaction::ContextStopped { id: context_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ContextNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT MODULE
// ============================================================================

pub mod context {
    use super::*;

    /// Actions for context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartActor { name: String },
        StopActor { name: String },
    }

    /// Reactions from context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ActorStarted { id: String },
        ActorStopped { id: String },
        ActorNotFound { name: String },
    }

    /// Context actor manages regular actors
    ///
    /// Responsible for:
    /// - Spawning and managing regular actors
    /// - Hierarchical addressing at context level
    /// - Lifecycle management of actors
    pub struct Actor {
        id: ActorId,
        actors: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new context actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                actors: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Context {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Context {} stopping", self.id.as_str());
                    // Stop all actors - children auto-stop
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Context {} shutting down", self.id.as_str());
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartActor { name } => {
                    if self.actors.contains_key(&name) {
                        let id = self.actors[&name].as_str().to_string();
                        return Ok(Reaction::ActorStarted { id });
                    }

                    // Spawn actor as child
                    let actor_id = actors.spawn(name.clone()).await?;
                    self.actors.insert(name, actor_id.clone());

                    tracing::info!("Context {} started actor {}", self.id.as_str(), actor_id.as_str());
                    Ok(Reaction::ActorStarted { id: actor_id.as_str().to_string() })
                }

                Action::StopActor { name } => {
                    if let Some(actor_id) = self.actors.remove(&name) {
                        tracing::info!("Context {} stopped actor {}", self.id.as_str(), actor_id.as_str());
                        Ok(Reaction::ActorStopped { id: actor_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ActorNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// ACTOR MODULE
// ============================================================================

pub mod actor {
    use super::*;

    /// Example counter action using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterAction {
        Start,
        Stop,
        Increment,
        Get,
        Spawn { name: String }, // Spawn child actor
    }

    /// Example counter reaction using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterReaction {
        Started,
        Stopped,
        Incremented { count: u32 },
        Count { value: u32 },
        Spawned { child: String }, // Child spawned
    }

    /// Example counter actor implementation
    ///
    /// Demonstrates:
    /// - Basic state management (count)
    /// - Child actor spawning via actors.spawn()
    /// - Inter-actor communication via actors.actor().act()
    pub struct Counter {
        id: ActorId,
        count: u32,
    }

    impl Counter {
        /// Create new counter actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                count: 0,
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Counter {
        type Action = CounterAction;
        type Reaction = CounterReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                CounterAction::Start => {
                    tracing::info!("Counter {} starting", self.id.as_str());
                    Ok(CounterReaction::Started)
                }

                CounterAction::Stop => {
                    tracing::info!("Counter {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(CounterReaction::Stopped)
                }

                CounterAction::Increment => {
                    self.count += 1;
                    tracing::debug!("Counter {} incremented to {}", self.id.as_str(), self.count);

                    // Example inter-actor communication:
                    // actors.actor("platform.com/web/main/other").act(CounterAction::Get).await?;

                    Ok(CounterReaction::Incremented { count: self.count })
                }

                CounterAction::Get => {
                    Ok(CounterReaction::Count { value: self.count })
                }

                CounterAction::Spawn { name } => {
                    // Spawn child actor using hierarchical addressing
                    let child_id = actors.spawn(name.clone()).await?;
                    tracing::info!("Counter {} spawned child: {}", self.id.as_str(), child_id.as_str());

                    // Could also communicate with child:
                    // actors.actor(child_id).act(CounterAction::Start).await?;

                    Ok(CounterReaction::Spawned { child: child_id.as_str().to_string() })
                }
            }
        }
    }
}

// ============================================================================
// CHAPTER 2: INFRASTRUCTURE - Alice Ryhl's Actor Pattern Implementation
// ============================================================================

/// Message envelope for actor communication following Alice Ryhl's pattern
///
/// This enum represents the two types of messages an actor can receive:
/// - Action messages that expect a response
/// - Shutdown messages that terminate the actor
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
///
/// This is the "task" part of the actor - it runs in its own tokio task
/// and processes messages from the handle via an mpsc channel
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
}

impl<T: Actor> ActorTask<T> {
    /// Create new actor task
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
    ) -> Self {
        Self { id, actor, receiver, actors }
    }

    /// Handle single message from the channel
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                // Call the actor's act method with the actors registry
                let result = self.actor.act(action, &self.actors).await;

                // Send response back if requested
                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                tracing::info!("Actor {} received shutdown signal", self.id.as_str());
                Ok(false) // Stop running
            }
        }
    }
}

/// Run actor task (following Alice Ryhl's pattern)
///
/// This function runs the main message loop for an actor task.
/// It continues until shutdown is requested or an error occurs.
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting task", task.id.as_str());

    // Main message loop - process messages until shutdown
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }

    tracing::info!("Actor {} task stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    /// Create new actor handle
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }

    /// Send action to actor and wait for reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();

        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };

        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;

        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Shutdown actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

/// Platform manages actor system and hides underlying runtime
pub struct Platform;

impl Platform {
    /// Create new platform
    pub fn new() -> Self {
        Self
    }

    /// Spawn actor on platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create actors registry for this actor
        let actors = Actors::child(actor_id.clone());

        let task = ActorTask::new(actor_id.clone(), actor, receiver, actors);
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// CHAPTER 3: COMPLETE EXAMPLE - Showcasing All TAMTIL Features
// ============================================================================

/// Complete example demonstrating all TAMTIL features
///
/// This example showcases:
/// - Hierarchical addressing: platform.com/context/context_id/actor/actor_id/child/child_id
/// - Actor spawning via actors.spawn(name)
/// - Inter-actor communication via actors.actor(id).act(action)
/// - Automatic child cleanup when parent stops
/// - rkyv zero-copy serialization
/// - Action→reaction pattern throughout
/// - Single-word naming convention
pub mod example {
    use super::*;

    /// Run complete TAMTIL feature demonstration
    pub async fn run() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 TAMTIL Complete Feature Demonstration");
        println!("========================================");

        // Demonstrate hierarchical addressing system
        demonstrate_addressing().await?;

        // Demonstrate actor spawning and communication
        demonstrate_spawning().await?;

        // Demonstrate rkyv serialization
        demonstrate_serialization().await?;

        // Demonstrate hierarchical shutdown
        demonstrate_shutdown().await?;

        println!("\n🎉 All TAMTIL features demonstrated successfully!");
        println!("\n💡 Key features showcased:");
        println!("   ✅ Hierarchical addressing: platform.com/context/context_id/actor/actor_id/child/child_id");
        println!("   ✅ Actor spawning: actors.spawn(name)");
        println!("   ✅ Inter-actor communication: actors.actor(id).act(action)");
        println!("   ✅ Automatic child cleanup on parent stop");
        println!("   ✅ rkyv zero-copy serialization");
        println!("   ✅ Action→reaction pattern");
        println!("   ✅ Single-word naming convention");
        println!("   ✅ No locking - elegant concurrency");

        Ok(())
    }

    /// Demonstrate hierarchical addressing system
    async fn demonstrate_addressing() -> TamtilResult<()> {
        println!("\n📍 Demonstrating hierarchical addressing system:");
        println!("   platform.com");
        println!("   ├── web (context)");
        println!("   │   ├── main (context instance)");
        println!("   │   │   ├── counter (actor)");
        println!("   │   │   │   ├── stats (actor instance)");
        println!("   │   │   │   │   └── session (child actor)");
        println!("   │   │   │   │       └── user123 (child instance)");

        // Test ActorId hierarchical methods
        let platform_id = ActorId::new("platform.com");
        let context_id = platform_id.child("web");
        let context_instance_id = context_id.child("main");
        let actor_id = context_instance_id.child("counter");
        let actor_instance_id = actor_id.child("stats");
        let child_id = actor_instance_id.child("session");
        let child_instance_id = child_id.child("user123");

        println!("\n✅ Address hierarchy created:");
        println!("   Platform: {}", platform_id.as_str());
        println!("   Context: {}", context_id.as_str());
        println!("   Context Instance: {}", context_instance_id.as_str());
        println!("   Actor: {}", actor_id.as_str());
        println!("   Actor Instance: {}", actor_instance_id.as_str());
        println!("   Child: {}", child_id.as_str());
        println!("   Child Instance: {}", child_instance_id.as_str());

        // Test parent/child relationships
        assert!(context_id.is_child_of(&platform_id));
        assert!(child_instance_id.is_child_of(&actor_instance_id));
        assert_eq!(child_id.parent(), Some(actor_instance_id.clone()));
        assert_eq!(child_instance_id.depth(), 7);

        println!("✅ Hierarchical relationships verified");

        Ok(())
    }

    /// Demonstrate actor spawning and communication
    async fn demonstrate_spawning() -> TamtilResult<()> {
        println!("\n🏗️  Demonstrating actor spawning and communication:");

        // Create platform actor
        let platform_id = ActorId::new("platform.com");
        let platform_actor = platform::Actor::new(platform_id.clone());
        let platform = Platform::new();
        let platform_handle = platform.spawn(platform_id.clone(), platform_actor).await?;

        // Start platform
        let reaction = platform_handle.act(platform::Action::Standard(StandardAction::Start)).await?;
        println!("✅ Platform started: {:?}", reaction);

        // Create context actor
        let context_id = ActorId::new("platform.com/web");
        let context_actor = context::Actor::new(context_id.clone());
        let context_handle = platform.spawn(context_id.clone(), context_actor).await?;

        // Start context
        let reaction = context_handle.act(context::Action::Standard(StandardAction::Start)).await?;
        println!("✅ Context started: {:?}", reaction);

        // Create counter actor
        let counter_id = ActorId::new("platform.com/web/main");
        let counter_actor = actor::Counter::new(counter_id.clone());
        let counter_handle = platform.spawn(counter_id.clone(), counter_actor).await?;

        // Start counter
        let reaction = counter_handle.act(actor::CounterAction::Start).await?;
        println!("✅ Counter started: {:?}", reaction);

        // Demonstrate child spawning
        let reaction = counter_handle.act(actor::CounterAction::Spawn { name: "stats".to_string() }).await?;
        println!("✅ Child spawned: {:?}", reaction);

        // Test counter functionality
        let reaction = counter_handle.act(actor::CounterAction::Increment).await?;
        println!("✅ Counter incremented: {:?}", reaction);

        let reaction = counter_handle.act(actor::CounterAction::Get).await?;
        println!("✅ Counter value: {:?}", reaction);

        Ok(())
    }

    /// Demonstrate rkyv serialization
    async fn demonstrate_serialization() -> TamtilResult<()> {
        println!("\n🗜️  Demonstrating rkyv zero-copy serialization:");

        let action = actor::CounterAction::Increment;
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<actor::CounterAction>, rkyv::rancor::Error>(&bytes).unwrap();

        println!("✅ Serialization: {:?} → {} bytes → {:?}", action, bytes.len(), archived);
        println!("✅ Zero-copy deserialization successful");

        Ok(())
    }

    /// Demonstrate hierarchical shutdown
    async fn demonstrate_shutdown() -> TamtilResult<()> {
        println!("\n🛑 Demonstrating hierarchical shutdown:");

        // Create a simple counter for shutdown demo
        let counter_id = ActorId::new("platform.com/demo");
        let counter_actor = actor::Counter::new(counter_id.clone());
        let platform = Platform::new();
        let counter_handle = platform.spawn(counter_id.clone(), counter_actor).await?;

        // Start and spawn children
        let _ = counter_handle.act(actor::CounterAction::Start).await?;
        let _ = counter_handle.act(actor::CounterAction::Spawn { name: "child1".to_string() }).await?;
        let _ = counter_handle.act(actor::CounterAction::Spawn { name: "child2".to_string() }).await?;

        println!("✅ Created parent with children");

        // Stop parent (children auto-stop)
        let reaction = counter_handle.act(actor::CounterAction::Stop).await?;
        println!("✅ Parent stopped (children auto-stopped): {:?}", reaction);

        Ok(())
    }
}

// ============================================================================
// CHAPTER 4: TESTS - Testing the Complete Example
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    /// Test the complete example functionality
    #[tokio::test]
    async fn test_complete_example() {
        // Run the complete example
        example::run().await.expect("Example should run successfully");
    }

    /// Test hierarchical addressing functionality
    #[tokio::test]
    async fn test_addressing_system() {
        // Test ActorId hierarchical methods
        let platform_id = ActorId::new("platform.com");
        let context_id = platform_id.child("web");
        let actor_id = context_id.child("main").child("counter");
        let child_id = actor_id.child("stats").child("session");

        // Test relationships
        assert!(context_id.is_child_of(&platform_id));
        assert!(child_id.is_child_of(&actor_id));
        assert_eq!(child_id.parent().unwrap().parent().unwrap(), actor_id);
        assert_eq!(child_id.depth(), 6);

        println!("✅ Addressing system tests passed");
    }

    /// Test actor spawning and communication
    #[tokio::test]
    async fn test_actor_communication() {
        let _ = tracing_subscriber::fmt::try_init();

        // Create counter actor
        let counter_id = ActorId::new("test-counter");
        let counter_actor = actor::Counter::new(counter_id.clone());
        let platform = Platform::new();
        let counter_handle = platform.spawn(counter_id, counter_actor).await.unwrap();

        // Test start
        let reaction = counter_handle.act(actor::CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, actor::CounterReaction::Started));

        // Test increment
        let reaction = counter_handle.act(actor::CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, actor::CounterReaction::Incremented { count: 1 }));

        // Test get
        let reaction = counter_handle.act(actor::CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, actor::CounterReaction::Count { value: 1 }));

        // Test spawning
        let reaction = counter_handle.act(actor::CounterAction::Spawn { name: "child".to_string() }).await.unwrap();
        assert!(matches!(reaction, actor::CounterReaction::Spawned { child } if child == "test-counter/child"));

        // Test stop
        let reaction = counter_handle.act(actor::CounterAction::Stop).await.unwrap();
        assert!(matches!(reaction, actor::CounterReaction::Stopped));

        println!("✅ Actor communication tests passed");
    }

    /// Test rkyv serialization
    #[tokio::test]
    async fn test_rkyv_serialization() {
        // Test action serialization
        let action = actor::CounterAction::Increment;
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<actor::CounterAction>, rkyv::rancor::Error>(&bytes).unwrap();

        // Verify serialization worked
        assert!(!bytes.is_empty());
        println!("✅ rkyv serialization: {:?} → {} bytes → {:?}", action, bytes.len(), archived);

        // Test reaction serialization
        let reaction = actor::CounterReaction::Count { value: 42 };
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&reaction).unwrap();
        let archived = rkyv::access::<rkyv::Archived<actor::CounterReaction>, rkyv::rancor::Error>(&bytes).unwrap();

        assert!(!bytes.is_empty());
        println!("✅ rkyv serialization: {:?} → {} bytes → {:?}", reaction, bytes.len(), archived);

        println!("✅ rkyv serialization tests passed");
    }
}
